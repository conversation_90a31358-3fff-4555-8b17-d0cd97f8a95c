#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的加密解密实现脚本
已破解的加密体系：
- AES加密：密钥 wtBfKcqAMug1wbH8，ECB模式，PKCS7填充
- MD5签名：密钥 xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY
"""

import base64
import hashlib
import json
import requests
from urllib.parse import urlencode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

class EncryptionSystem:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"

        # AES加密参数
        self.aes_key = "wtBfKcqAMug1wbH8"  # 16字节密钥
        self.aes_mode = AES.MODE_ECB

        # 签名参数
        self.sign_key = "xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY"

        # API接口
        self.captcha_api = "/dev-api/api/login/authccode.html"
        self.register_api = "/dev-api/api/login/register.html"
        
        # 请求头模板
        self.headers = {
            'accept': '*/*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'content-type': 'application/json',
            'is_app': 'false',
            'origin': 'https://ds-web1.yrpdz.com',
            'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'token': 'transfersecret'
        }

    def encrypt_data(self, data):
        """AES加密数据"""
        try:
            # 将数据转换为JSON字符串
            json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)

            # 转换为字节
            data_bytes = json_str.encode('utf-8')

            # PKCS7填充
            padded_data = pad(data_bytes, AES.block_size)

            # AES加密
            cipher = AES.new(self.aes_key.encode('utf-8'), self.aes_mode)
            encrypted = cipher.encrypt(padded_data)

            # Base64编码
            encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')

            return encrypted_b64

        except Exception as e:
            print(f"加密失败: {e}")
            return None

    def decrypt_data(self, encrypted_data):
        """AES解密数据"""
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)

            # AES解密
            cipher = AES.new(self.aes_key.encode('utf-8'), self.aes_mode)
            decrypted = cipher.decrypt(encrypted_bytes)

            # 去除填充
            unpadded_data = unpad(decrypted, AES.block_size)

            # 转换为字符串
            json_str = unpadded_data.decode('utf-8')

            # 解析JSON
            return json.loads(json_str)

        except Exception as e:
            print(f"解密失败: {e}")
            return None

    def generate_sign(self, data):
        """生成签名"""
        try:
            # 如果data是字符串，先解密
            if isinstance(data, str):
                decrypted_data = self.decrypt_data(data)
                if not decrypted_data:
                    return None
            else:
                decrypted_data = data

            # 按key排序
            sorted_data = dict(sorted(decrypted_data.items()))

            # 生成查询字符串
            query_string = urlencode(sorted_data, doseq=True)

            # 拼接签名密钥
            sign_string = f"{query_string}&key={self.sign_key}"

            # MD5签名
            sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest()

            return sign

        except Exception as e:
            print(f"签名生成失败: {e}")
            return None

    def make_request(self, api_path, data):
        """发送加密请求"""
        try:
            # 加密数据
            encrypted_data = self.encrypt_data(data)
            if not encrypted_data:
                return None

            # 生成签名
            sign = self.generate_sign(data)
            if not sign:
                return None

            # 设置请求头
            headers = self.headers.copy()
            headers['sign'] = sign
            headers['transfersecret'] = encrypted_data

            # 构造请求体 (根据JS代码，请求体是 {加密数据: 长加密数据} 的格式)
            request_body = {encrypted_data: encrypted_data}

            # 发送请求
            response = requests.post(
                f"{self.base_url}{api_path}",
                headers=headers,
                json=request_body,
                timeout=10
            )

            print(f"请求状态码: {response.status_code}")
            print(f"响应内容: {response.text}")

            # 解密响应
            if response.status_code == 200:
                decrypted_response = self.decrypt_data(response.text)
                return decrypted_response

            return None

        except Exception as e:
            print(f"请求失败: {e}")
            return None

    def test_captcha_request(self):
        """测试验证码请求"""
        print("=== 测试验证码请求 ===")

        # 验证码请求通常是空数据或简单参数
        captcha_data = {}

        result = self.make_request(self.captcha_api, captcha_data)
        return result

    def test_register_request(self, phone, password, code):
        """测试注册请求"""
        print("=== 测试注册请求 ===")

        register_data = {
            "phone": phone,
            "password": password,
            "code": code
        }

        result = self.make_request(self.register_api, register_data)
        return result

    def test_encryption_system(self):
        """测试加密解密系统"""
        print("=== 测试加密解密系统 ===")

        # 测试数据
        test_data = {"test": "hello", "number": 123}

        print(f"原始数据: {test_data}")

        # 加密
        encrypted = self.encrypt_data(test_data)
        print(f"加密结果: {encrypted}")

        # 解密
        decrypted = self.decrypt_data(encrypted)
        print(f"解密结果: {decrypted}")

        # 生成签名
        sign = self.generate_sign(test_data)
        print(f"签名结果: {sign}")

        return encrypted, decrypted, sign


if __name__ == "__main__":
    # 创建加密系统实例
    crypto_system = EncryptionSystem()

    print("🎉 加密系统已破解！")
    print("=" * 50)
    print(f"AES密钥: {crypto_system.aes_key}")
    print(f"签名密钥: {crypto_system.sign_key}")
    print("=" * 50)

    # 测试加密解密系统
    crypto_system.test_encryption_system()

    print("\n" + "=" * 50)
    print("现在可以进行实际的API请求测试:")
    print("1. 验证码请求")
    print("2. 注册请求")

    # 测试验证码请求
    print("\n" + "-" * 30)
    crypto_system.test_captcha_request()

    # 如果需要测试注册，可以取消注释下面的代码
    # print("\n" + "-" * 30)
    # crypto_system.test_register_request("13800138000", "123456", "1234")
