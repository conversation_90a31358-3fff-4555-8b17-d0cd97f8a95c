#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 鲲鹏通讯 - 自动注册脚本 (V4 - 动态设备指纹 & 交互式邀请码 & 预请求)

import requests
import json
import base64
import random
import string
import time
import hmac
import hashlib
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad, unpad

# --- 配置项 ---
BASE_URL = "http://ggregesd.kes337f.shop"
VERIFY_URL = f"{BASE_URL}/verify/telephone"
REGISTER_URL = f"{BASE_URL}/user/register/v1"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在

# 全局加密密钥 (模式二：全局固定密钥，已验证有效)
GLOBAL_AES_KEY = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
GLOBAL_AES_IV = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")
# 用于密码加密的IV
PASSWORD_STATIC_IV = bytes.fromhex('0102030405060708090a0b0c0d0e0f10')

# --- 辅助函数 ---

def generate_random_phone():
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '150', '151', '152', '153', '155', '156', '157', '158', '159', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password(length=10):
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for i in range(length))

def generate_random_chinese_name():
    surnames = "赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜"
    given_names = "伟芳娜秀英敏静丽强磊军洋勇杰娟涛明超平刚桂"
    surname = random.choice(surnames)
    given_name = "".join(random.choices(given_names, k=random.randint(1, 2)))
    return f"{surname}{given_name}"

# --- [新功能] 动态设备指纹生成函数 ---
def generate_dynamic_fingerprint():
    """随机生成一套逼真的安卓设备指纹"""
    models = [
        'SM-G9980', '2201122C', 'V218A', 'OPPO K9x', 'Realme GT Neo',
        'Redmi K50', 'Vivo X80', 'Honor 70', 'Huawei P50', 'Xiaomi 13 Pro'
    ]
    os_versions = ['11', '12', '13', '14']
    if random.choice([True, False]):
        serial = ''.join(random.choices(string.digits, k=15)) # 模拟IMEI
    else:
        serial = ''.join(random.choices(string.hexdigits.lower(), k=16)) # 模拟硬件ID
    return {"model": random.choice(models), "os_version": random.choice(os_versions), "serial": serial}

# --- 加密与请求函数 (保留您验证过的正确逻辑) ---

def encrypt_login_password(password: str) -> str:
    password_bytes = password.encode('utf-8')
    md5_hash_bytes = hashlib.md5(password_bytes).digest()
    cipher = AES.new(md5_hash_bytes, AES.MODE_CBC, PASSWORD_STATIC_IV)
    padded_data = pad(md5_hash_bytes, AES.block_size)
    aes_encrypted_bytes = cipher.encrypt(padded_data)
    return hashlib.md5(aes_encrypted_bytes).hexdigest()

def generate_mac_signature(key: bytes, content: str) -> str:
    h = hmac.new(key, content.encode('utf-8'), hashlib.md5)
    return base64.b64encode(h.digest()).decode('utf-8')

def encrypt_request_data(payload: dict) -> str:
    payload_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    padded_data = pad(payload_str.encode('utf-8'), AES.block_size)
    return base64.b64encode(cipher.encrypt(padded_data)).decode('utf-8')

def decrypt_response_data(encrypted_data_b64: str) -> dict:
    encrypted_bytes = base64.b64decode(encrypted_data_b64)
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    decrypted_padded_bytes = cipher.decrypt(encrypted_bytes)
    return json.loads(unpad(decrypted_padded_bytes, AES.block_size).decode('utf-8'))

# --- [新功能] 手机号验证预请求函数 ---
def verify_telephone_pre_request(session, phone, area_code, language, headers):
    print("\n--- [步骤1] 正在发送手机号验证预请求 ---")
    salt = str(int(time.time() * 1000))
    secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
    params = {'areaCode': area_code, 'telephone': phone, 'language': language, 'salt': salt, 'secret': secret}
    try:
        response = session.get(VERIFY_URL, params=params, headers=headers)
        response.raise_for_status()
        response_json = response.json()
        print("服务器响应:", json.dumps(response_json, ensure_ascii=False))
        if response_json.get("resultCode") == 1:
            print(">>> 手机号预验证成功，服务器已授权后续注册。")
            return True
        else:
            print(f">>> 手机号预验证失败: {response_json.get('resultMsg', '未知错误')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"预请求网络错误: {e}")
        return False

# --- 主函数 (整合了所有新功能) ---
def auto_register():
    try:
        # [功能1] 交互式输入邀请码
        invite_code = input("请输入您的邀请码并按回车: ").strip()
        if not invite_code:
            print("错误：邀请码不能为空。程序已退出。")
            return

        # 固定的元数据
        area_code = "92"
        sex = "1"
        birthday = "1753510077"
        language = "zh"
        
        # [功能2] 动态生成设备指纹
        print("\n--- [动态] 正在生成本次注册信息 ---")
        fingerprint = generate_dynamic_fingerprint()
        device_model = fingerprint['model']
        device_os_version = fingerprint['os_version']
        device_serial = fingerprint['serial']

        # 生成其他随机信息
        phone = generate_random_phone()
        password = generate_random_password()
        nickname = f"{generate_random_chinese_name()}-黄朝凤团队"
        
        print(f"  手机号: {phone}\n  密码: {password}\n  昵称: {nickname}")
        print(f"  设备型号: {device_model}\n  安卓版本: {device_os_version}\n  设备序列号: {device_serial}")
        print(f"  邀请码: {invite_code} (由您输入)")

        session = requests.Session()
        headers = {
            "User-Agent": f"chat_im/2.1.8 (Linux; U; Android {device_os_version}; {device_model} Build/UP1A.231005.007)",
            "Connection": "Keep-Alive",
            "Accept-Encoding": "gzip"
        }
    
        # [功能3] 执行预请求
        if not verify_telephone_pre_request(session, phone, area_code, language, headers):
            print("\n!!! 流程终止，无法进行下一步注册。 !!!")
            return

        print("\n--- [步骤2] 准备正式注册请求 ---")
        salt = str(int(time.time() * 1000))
        encrypted_password = encrypt_login_password(password)
        mac_content_to_sign = f"212i919292901{area_code}{language}{phone}{sex}{salt}"
        mac_signature = generate_mac_signature(GLOBAL_AES_KEY, mac_content_to_sign)

        payload = {
            "birthday": birthday, "smsCode": "", "sex": sex, "telephone": phone,
            "cityId": "0", "provinceId": "0", "countryId": "0", "mac": mac_signature,
            "password": encrypted_password, "areaCode": area_code, "areaId": "0",
            "apiVersion": "76", "osVersion": device_os_version, "serial": device_serial,
            "inviteCode": invite_code, "idcard": "", "nickname": nickname,
            "isSmsRegister": "0", "name": "", "xmppVersion": "1",
            "model": device_model, "userType": "0"
        }
        
        encrypted_data = encrypt_request_data(payload)
        random_secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
        params = {"data": encrypted_data, "deviceId": "android", "language": language, "salt": salt, "secret": random_secret}

        print("--- 正在发送正式注册请求 ---")
        response = session.get(REGISTER_URL, params=params, headers=headers)
        response.raise_for_status()
        response_json = response.json()

        print("\n--- 服务器原始响应 ---")
        print(json.dumps(response_json, indent=2, ensure_ascii=False))

        if "data" in response_json and isinstance(response_json.get("data"), dict) and "data" in response_json["data"]:
            print("\n--- 尝试解密响应内容 ---")
            decrypted_resp = decrypt_response_data(response_json["data"]["data"])
            print(">>> 响应解密成功! <<<")
            print(json.dumps(decrypted_resp, indent=2, ensure_ascii=False))
            if response_json.get("resultCode") == 1:
                print("\n\n>>> 逻辑判断：\033[92m注册成功!\033[0m <<<") # 使用绿色高亮
                with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                    f.write(f"{phone}:{password}:{nickname}\n")
                print(f"\n账号信息已保存到: {OUTPUT_FILE}")
            else:
                print(f"\n\n>>> 逻辑判断：\033[91m注册失败 (ResultCode: {response_json.get('resultCode')}) - {response_json.get('resultMsg', '')}\033[0m <<<")
        else:
            error_msg = response_json.get("resultMsg", "未知错误")
            print(f"\n\n>>> 逻辑判断：\033[91m注册失败 ({error_msg})\033[0m <<<")

    except requests.exceptions.RequestException as e:
        print(f"\n网络请求错误: {e}")
    except KeyboardInterrupt:
        print("\n\n操作已由用户手动中断。程序退出。")
    except Exception as e:
        print(f"\n发生未知错误: {e}")

if __name__ == "__main__":
    try:
        while True:
            auto_register()
            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("-" * 50)
    except KeyboardInterrupt:
        print("\n\n程序已退出。")